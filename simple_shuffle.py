#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版文本行打乱工具
快速打乱指定txt文件的行顺序
"""

import random
import sys
import os

def shuffle_lines(filename):
    """打乱文件行顺序"""
    
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"错误: 文件 '{filename}' 不存在")
        return False
    
    try:
        # 读取所有行
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"读取到 {len(lines)} 行数据")
        
        # 打乱顺序
        random.shuffle(lines)
        
        # 写回文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"成功打乱 {filename} 的行顺序！")
        return True
        
    except Exception as e:
        print(f"处理失败: {e}")
        return False

if __name__ == "__main__":
    # 获取文件名参数
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = "freekey.txt"  # 默认文件名
    
    print(f"正在处理文件: {filename}")
    shuffle_lines(filename)
