#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本行顺序打乱工具
用于随机打乱txt文件中的行顺序

使用方法:
    python shuffle_lines.py                    # 处理当前目录下的freekey.txt
    python shuffle_lines.py filename.txt       # 处理指定文件
    python shuffle_lines.py -h                 # 显示帮助信息

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import random
import shutil
import argparse
from datetime import datetime


def create_backup(file_path):
    """创建文件备份"""
    try:
        backup_path = f"{file_path}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ 创建备份失败: {e}")
        return None


def shuffle_file_lines(file_path, create_backup_flag=True):
    """
    打乱文件行顺序
    
    Args:
        file_path (str): 文件路径
        create_backup_flag (bool): 是否创建备份
    
    Returns:
        bool: 操作是否成功
    """
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"✗ 错误: 文件 '{file_path}' 不存在")
        return False
    
    # 检查文件是否为空
    if os.path.getsize(file_path) == 0:
        print(f"✗ 错误: 文件 '{file_path}' 为空")
        return False
    
    print(f"📁 正在处理文件: {file_path}")
    
    try:
        # 创建备份
        if create_backup_flag:
            backup_path = create_backup(file_path)
            if not backup_path:
                response = input("备份失败，是否继续处理？(y/N): ")
                if response.lower() != 'y':
                    return False
        
        # 读取文件所有行
        print("📖 正在读取文件...")
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        original_count = len(lines)
        print(f"📊 原文件共有 {original_count} 行")
        
        if original_count == 0:
            print("✗ 文件内容为空")
            return False
        
        # 打乱行顺序
        print("🔀 正在打乱行顺序...")
        random.shuffle(lines)
        
        # 写入打乱后的内容
        print("💾 正在保存打乱后的文件...")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        # 验证结果
        with open(file_path, 'r', encoding='utf-8') as f:
            new_lines = f.readlines()
        
        new_count = len(new_lines)
        
        print(f"✓ 处理完成!")
        print(f"📊 处理结果:")
        print(f"   - 原始行数: {original_count}")
        print(f"   - 处理后行数: {new_count}")
        print(f"   - 数据完整性: {'✓ 正常' if original_count == new_count else '✗ 异常'}")
        
        return True
        
    except UnicodeDecodeError:
        print("✗ 错误: 文件编码不是UTF-8，请检查文件编码")
        return False
    except PermissionError:
        print("✗ 错误: 没有权限访问文件，请检查文件权限")
        return False
    except Exception as e:
        print(f"✗ 处理过程中发生错误: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="文本行顺序打乱工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python shuffle_lines.py                    # 处理freekey.txt
  python shuffle_lines.py myfile.txt         # 处理指定文件
  python shuffle_lines.py --no-backup file.txt  # 不创建备份
        """
    )
    
    parser.add_argument(
        'filename', 
        nargs='?', 
        default='freekey.txt',
        help='要处理的文件名 (默认: freekey.txt)'
    )
    
    parser.add_argument(
        '--no-backup', 
        action='store_true',
        help='不创建备份文件'
    )
    
    parser.add_argument(
        '--version', 
        action='version', 
        version='%(prog)s 1.0'
    )
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("🎲 文本行顺序打乱工具 v1.0")
    print("=" * 50)
    
    # 执行打乱操作
    success = shuffle_file_lines(args.filename, not args.no_backup)
    
    if success:
        print("\n🎉 任务完成！文件行顺序已成功打乱。")
    else:
        print("\n❌ 任务失败！请检查错误信息并重试。")
        sys.exit(1)


if __name__ == "__main__":
    main()
